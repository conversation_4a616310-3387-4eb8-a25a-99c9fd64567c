/**
 * Test API for Enhanced Agent Collaboration System
 * Tests the new sequential consultation, synthesis, and context-aware features
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const testType = searchParams.get('type') || 'basic';

  try {
    if (testType === 'basic') {
      return await testBasicEnhancedCollaboration();
    } else if (testType === 'synthesis') {
      return await testSynthesisEngine();
    } else if (testType === 'context') {
      return await testContextualCollaboration();
    } else {
      return NextResponse.json(
        { error: 'Invalid test type. Use: basic, synthesis, or context' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Enhanced collaboration test error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed', 
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function testBasicEnhancedCollaboration() {
  console.log('🧪 Testing Enhanced Agent Collaboration System...');
  
  try {
    // Create a test artifact
    const testArtifact = {
      id: 'test-artifact-enhanced',
      type: 'blog-post',
      content: 'This is a test blog post about AI and machine learning. It needs SEO optimization and better structure for our target audience of developers.',
      metadata: {
        title: 'Test Article',
        wordCount: 150
      }
    };

    // Test feedback
    const testFeedback = 'The content needs better SEO optimization, more audience targeting, and improved structure for readability.';

    // Call the enhanced collaboration API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/agents/collaboration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        artifactId: testArtifact.id,
        feedback: testFeedback,
        workflowExecutionId: 'test-workflow-enhanced',
        stepId: 'test-step',
        collaborationType: 'feedback_analysis'
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // Validate enhanced features
    const validationResults = {
      basicCollaboration: !!result.success,
      hasConsultations: result.data?.consultations?.length > 0,
      hasSynthesis: !!result.data?.synthesis,
      hasQualityScore: typeof result.data?.synthesis?.qualityScore === 'number',
      hasConsensusAreas: Array.isArray(result.data?.synthesis?.consensusAreas),
      hasImplementationPlan: Array.isArray(result.data?.synthesis?.implementationPlan),
      hasPrioritizedSuggestions: Array.isArray(result.data?.synthesis?.prioritizedSuggestions),
      enhancedSummary: !!result.data?.summary?.qualityScore
    };

    const allTestsPassed = Object.values(validationResults).every(Boolean);

    return NextResponse.json({
      success: allTestsPassed,
      testType: 'basic',
      validationResults,
      collaborationData: {
        agentsConsulted: result.data?.consultations?.length || 0,
        qualityScore: result.data?.synthesis?.qualityScore || 0,
        consensusAreas: result.data?.synthesis?.consensusAreas?.length || 0,
        implementationPhases: result.data?.synthesis?.implementationPlan?.length || 0,
        totalSuggestions: result.data?.summary?.totalSuggestions || 0
      },
      message: allTestsPassed ? 
        '✅ Enhanced collaboration system working correctly' : 
        '❌ Some enhanced features not working properly'
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      testType: 'basic',
      error: error instanceof Error ? error.message : 'Unknown error',
      message: '❌ Enhanced collaboration test failed'
    });
  }
}

async function testSynthesisEngine() {
  console.log('🧪 Testing Synthesis Engine...');
  
  // This would test the synthesis engine with mock data
  // For now, return a placeholder response
  return NextResponse.json({
    success: true,
    testType: 'synthesis',
    message: '🔬 Synthesis engine test - placeholder implementation',
    note: 'Full synthesis testing requires integration with the collaboration API'
  });
}

async function testContextualCollaboration() {
  console.log('🧪 Testing Contextual Collaboration...');
  
  // This would test context passing between agents
  // For now, return a placeholder response
  return NextResponse.json({
    success: true,
    testType: 'context',
    message: '🔗 Contextual collaboration test - placeholder implementation',
    note: 'Full context testing requires multiple collaboration rounds'
  });
}
