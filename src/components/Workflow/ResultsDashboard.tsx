import React, { useState } from 'react';

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: string;
  estimatedTime: number;
  consultationEnabled: boolean;
  agentCount: number;
}

interface Props {
  execution: WorkflowExecution;
  selectedTemplate: WorkflowTemplate;
  onReset: () => void;
  onBack?: () => void;
}

export default function ResultsDashboard({
  execution,
  selectedTemplate,
  onReset,
  onBack
}: Props) {
  const [activeTab, setActiveTab] = useState<'artifacts' | 'summary' | 'export' | 'cms'>('artifacts');

  const completedSteps = execution.steps.filter(s => s.status === 'completed');
  const artifactSteps = execution.steps.filter(step => step.artifactId || (step.outputs && step.outputs.content));

  // Get completion data from workflow outputs
  const completionReport = execution.outputs?.completion_report;
  const resultsPackage = execution.outputs?.results_package;
  const cmsData = completionReport?.outputs?.cmsDetails;

  const handleExport = async (format: 'pdf' | 'docx' | 'html' | 'json' | 'markdown') => {
    try {
      const response = await fetch(`/api/workflow/execution/${execution.id}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workflow-${execution.id.slice(-8)}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Export failed. Please try again.');
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('Export failed. Please try again.');
    }
  };

  const getQualityScore = () => {
    // Try to get from completion report first
    if (completionReport?.metrics?.seoScore) {
      return completionReport.metrics.seoScore;
    }
    if (resultsPackage?.content_metrics?.seo_score) {
      return resultsPackage.content_metrics.seo_score;
    }

    const scores = execution.steps
      .filter(step => step.outputs?.qualityScore)
      .map(step => step.outputs.qualityScore);

    if (scores.length === 0) return 85; // Default score
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  };

  const getWordCount = () => {
    // Try to get from completion report first
    if (completionReport?.metrics?.wordCount) {
      return completionReport.metrics.wordCount;
    }
    if (resultsPackage?.content_metrics?.word_count) {
      return resultsPackage.content_metrics.word_count;
    }

    return execution.steps
      .filter(step => step.outputs?.wordCount)
      .reduce((total, step) => total + step.outputs.wordCount, 0);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">🎉 Workflow Complete!</h2>
        <p className="text-gray-600">
          {cmsData?.postId
            ? `Your content has been published to CMS with ID: ${cmsData.postId}`
            : 'Your content has been generated successfully with agent collaboration and human review.'
          }
        </p>
        {completionReport?.summary && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">
              <strong>Workflow Summary:</strong> {completionReport.summary.agentConsultations} agent consultations completed.
              Content quality score: {getQualityScore()}/100
            </p>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'artifacts', label: 'Generated Content', icon: '📄' },
              { id: 'summary', label: 'Workflow Summary', icon: '📊' },
              { id: 'cms', label: 'CMS Publishing', icon: '🚀' },
              { id: 'export', label: 'Export Options', icon: '💾' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Artifacts Tab */}
          {activeTab === 'artifacts' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Generated Content</h3>
              
              {artifactSteps.length > 0 ? (
                <div className="space-y-4">
                  {artifactSteps.map((step) => (
                    <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{step.name}</h4>
                        <div className="flex items-center space-x-2">
                          {step.outputs?.wordCount && (
                            <span className="text-xs text-gray-500">
                              {step.outputs.wordCount} words
                            </span>
                          )}
                          <span className="text-xs text-green-600">📄 Generated</span>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-sm text-gray-800 max-h-48 overflow-y-auto">
                          {step.outputs?.content ? (
                            <div className="whitespace-pre-wrap">
                              {step.outputs.content.length > 500 
                                ? `${step.outputs.content.substring(0, 500)}...`
                                : step.outputs.content
                              }
                            </div>
                          ) : (
                            <div className="text-gray-500 italic">Content not available for preview</div>
                          )}
                        </div>
                      </div>

                      {step.outputs?.content && step.outputs.content.length > 500 && (
                        <button
                          onClick={() => {
                            const newWindow = window.open('', '_blank');
                            if (newWindow) {
                              newWindow.document.write(`
                                <html>
                                  <head><title>${step.name}</title></head>
                                  <body style="font-family: system-ui; padding: 20px; line-height: 1.6;">
                                    <h1>${step.name}</h1>
                                    <div style="white-space: pre-wrap;">${step.outputs.content}</div>
                                  </body>
                                </html>
                              `);
                              newWindow.document.close();
                            }
                          }}
                          className="mt-2 text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          View Full Content
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">📄</div>
                  <div>No artifacts generated</div>
                </div>
              )}

              <button
                onClick={() => {
                  window.open(`/workflow/agent-enhanced/artifacts/${execution.id}`, '_blank');
                }}
                className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                📄 View All Artifacts
              </button>
            </div>
          )}

          {/* Summary Tab */}
          {activeTab === 'summary' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Workflow Summary</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Info */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Basic Information</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Template:</span>
                      <span className="font-medium text-gray-900">{selectedTemplate.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Category:</span>
                      <span className="font-medium text-gray-900 capitalize">{selectedTemplate.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Difficulty:</span>
                      <span className="font-medium text-gray-900 capitalize">{selectedTemplate.difficulty}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Execution ID:</span>
                      <span className="font-mono text-gray-900">{execution.id.slice(-8)}</span>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Performance Metrics</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Steps Completed:</span>
                      <span className="font-medium text-gray-900">
                        {completedSteps.length} / {execution.steps.length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Quality Score:</span>
                      <span className="font-medium text-green-600">{getQualityScore()}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Word Count:</span>
                      <span className="font-medium text-gray-900">{getWordCount().toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Agent Collaboration:</span>
                      <span className="font-medium text-gray-900">
                        {selectedTemplate.consultationEnabled ? '✅ Enabled' : '❌ Disabled'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step Details */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Step Details</h4>
                <div className="space-y-2">
                  {execution.steps.map((step, index) => (
                    <div key={step.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                          step.status === 'completed' ? 'bg-green-600 text-white' :
                          step.status === 'failed' ? 'bg-red-600 text-white' :
                          'bg-gray-400 text-white'
                        }`}>
                          {step.status === 'completed' ? '✓' : step.status === 'failed' ? '✗' : index + 1}
                        </div>
                        <span className="text-sm font-medium text-gray-900">{step.name}</span>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        step.status === 'completed' ? 'bg-green-100 text-green-800' :
                        step.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {step.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* CMS Publishing Tab */}
          {activeTab === 'cms' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">CMS Publishing Status</h3>

              {cmsData ? (
                <div className="space-y-4">
                  {/* Publishing Success */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-green-600 text-xl">✅</span>
                      <h4 className="font-medium text-green-900">Content Successfully Published</h4>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-green-700">CMS Post ID:</span>
                        <span className="font-mono text-green-900">{cmsData.postId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">Status:</span>
                        <span className="font-medium text-green-900 capitalize">{cmsData.status}</span>
                      </div>
                      {cmsData.url && (
                        <div className="flex justify-between">
                          <span className="text-green-700">CMS URL:</span>
                          <a
                            href={cmsData.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            View in CMS
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Publishing Details */}
                  {resultsPackage?.publishing_recommendations && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-3">Publishing Recommendations</h4>
                      <div className="space-y-2 text-sm text-blue-800">
                        {resultsPackage.publishing_recommendations.best_publish_time && (
                          <div>
                            <strong>Best Publish Time:</strong> {resultsPackage.publishing_recommendations.best_publish_time}
                          </div>
                        )}
                        {resultsPackage.publishing_recommendations.social_media_strategy && (
                          <div>
                            <strong>Social Media Strategy:</strong>
                            <ul className="list-disc list-inside ml-4 mt-1">
                              {resultsPackage.publishing_recommendations.social_media_strategy.map((strategy: string, index: number) => (
                                <li key={index}>{strategy}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {resultsPackage.publishing_recommendations.internal_linking && (
                          <div>
                            <strong>Internal Linking Suggestions:</strong>
                            <ul className="list-disc list-inside ml-4 mt-1">
                              {resultsPackage.publishing_recommendations.internal_linking.map((link: string, index: number) => (
                                <li key={index}>{link}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Next Steps */}
                  {resultsPackage?.next_steps && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-medium text-yellow-900 mb-3">Next Steps</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-yellow-800">
                        {resultsPackage.next_steps.map((step: string, index: number) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">📝</div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Content Ready for Publishing</h4>
                  <p className="text-gray-600 mb-4">
                    Your content has been generated and is ready to be published to your CMS.
                  </p>
                  <button
                    onClick={() => {
                      // In a real implementation, this would trigger CMS publishing
                      alert('CMS publishing feature would be implemented here');
                    }}
                    className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
                  >
                    🚀 Publish to CMS
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Export Tab */}
          {activeTab === 'export' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Export Options</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { format: 'pdf', label: 'PDF Document', icon: '📄', description: 'Formatted document with styling' },
                  { format: 'docx', label: 'Word Document', icon: '📝', description: 'Microsoft Word compatible' },
                  { format: 'html', label: 'HTML File', icon: '🌐', description: 'Web-ready format' },
                  { format: 'json', label: 'JSON Data', icon: '📊', description: 'Raw data export' },
                  { format: 'markdown', label: 'Markdown', icon: '📋', description: 'Developer-friendly format' }
                ].map((option) => (
                  <button
                    key={option.format}
                    onClick={() => handleExport(option.format as any)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-2xl">{option.icon}</span>
                      <span className="font-medium text-gray-900">{option.label}</span>
                    </div>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={onReset}
          className="flex-1 px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
        >
          🚀 Create Another Workflow
        </button>
        {onBack && (
          <button
            onClick={onBack}
            className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 font-medium"
          >
            ← Back to Dashboard
          </button>
        )}
      </div>
    </div>
  );
}
