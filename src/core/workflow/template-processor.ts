/**
 * Template Processor
 * Converts workflow templates to runtime workflows with approval gates
 */

import { v4 as uuidv4 } from 'uuid';
import {
  WorkflowTemplate,
  Workflow,
  WorkflowStep,
  StepType,
  ApprovalGate,
  ArtifactType
} from './types';

export interface ProcessedWorkflow {
  workflow: Workflow;
  approvalGates: ApprovalGate[];
}

export class TemplateProcessor {
  /**
   * Process a template into a runtime workflow with approval gates
   */
  static processTemplate(template: WorkflowTemplate, userId?: string): ProcessedWorkflow {
    const workflow: Workflow = {
      ...template.workflow,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const approvalGates: ApprovalGate[] = [];

    // Process each step to extract approval gates
    workflow.steps.forEach(step => {
      if (step.type === StepType.APPROVAL_GATE) {
        const approvalGate = this.createApprovalGateFromStep(step, userId);
        approvalGates.push(approvalGate);
      }
    });

    return {
      workflow,
      approvalGates
    };
  }

  /**
   * Create approval gate configuration from workflow step
   */
  private static createApprovalGateFromStep(step: WorkflowStep, userId?: string): ApprovalGate {
    const artifactType = this.inferArtifactTypeFromStep(step);
    
    return {
      id: uuidv4(),
      stepId: step.id,
      artifactType,
      approvers: userId ? [userId] : ['system'], // Default to user or system
      requiredApprovals: 1,
      autoApprove: false,
      timeout: 24, // 24 hours default
      escalation: {
        enabled: false,
        escalateAfterHours: 48,
        escalateTo: [],
        maxEscalations: 1
      }
    };
  }

  /**
   * Infer artifact type from step configuration
   */
  private static inferArtifactTypeFromStep(step: WorkflowStep): ArtifactType {
    const stepName = step.name.toLowerCase();
    
    if (stepName.includes('keyword')) {
      return ArtifactType.KEYWORD_RESEARCH;
    } else if (stepName.includes('content') && stepName.includes('strategy')) {
      return ArtifactType.CONTENT_STRATEGY;
    } else if (stepName.includes('content')) {
      return ArtifactType.CONTENT_DRAFT;
    } else if (stepName.includes('seo')) {
      return ArtifactType.SEO_OPTIMIZATION;
    } else if (stepName.includes('final')) {
      return ArtifactType.FINAL_CONTENT;
    } else {
      return ArtifactType.CONTENT_DRAFT;
    }
  }

  /**
   * Validate template structure
   */
  static validateTemplate(template: WorkflowTemplate): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check basic template structure
    if (!template.id) {
      errors.push('Template must have an ID');
    }

    if (!template.workflow) {
      errors.push('Template must have a workflow definition');
      return { valid: false, errors, warnings };
    }

    if (!template.workflow.steps || template.workflow.steps.length === 0) {
      errors.push('Workflow must have at least one step');
    }

    // Validate step dependencies
    const stepIds = new Set(template.workflow.steps.map(s => s.id));
    
    template.workflow.steps.forEach(step => {
      step.dependencies.forEach(depId => {
        if (!stepIds.has(depId)) {
          errors.push(`Step ${step.id} depends on non-existent step ${depId}`);
        }
      });

      // Check for circular dependencies (basic check)
      if (step.dependencies.includes(step.id)) {
        errors.push(`Step ${step.id} cannot depend on itself`);
      }
    });

    // Validate approval gates
    const approvalGates = template.workflow.steps.filter(s => s.type === StepType.APPROVAL_GATE);
    
    approvalGates.forEach(gate => {
      if (!gate.config.reviewConfig) {
        warnings.push(`Approval gate ${gate.id} missing review configuration`);
      }

      if (gate.dependencies.length === 0) {
        warnings.push(`Approval gate ${gate.id} has no dependencies - it will execute immediately`);
      }
    });

    // Check for orphaned steps (steps with no path to them)
    const reachableSteps = new Set<string>();
    const dependentSteps = new Map<string, string[]>(); // stepId -> steps that depend on it

    // Build dependency map
    template.workflow.steps.forEach(step => {
      step.dependencies.forEach(depId => {
        if (!dependentSteps.has(depId)) {
          dependentSteps.set(depId, []);
        }
        dependentSteps.get(depId)!.push(step.id);
      });
    });

    const findReachableSteps = (stepId: string) => {
      if (reachableSteps.has(stepId)) return;
      reachableSteps.add(stepId);

      // Find steps that depend on this step and mark them as reachable
      const dependents = dependentSteps.get(stepId) || [];
      dependents.forEach(depStepId => findReachableSteps(depStepId));
    };

    // Start from steps with no dependencies (entry points)
    const entrySteps = template.workflow.steps.filter(s => s.dependencies.length === 0);
    entrySteps.forEach(step => findReachableSteps(step.id));

    template.workflow.steps.forEach(step => {
      if (!reachableSteps.has(step.id)) {
        warnings.push(`Step ${step.id} may be unreachable`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get template execution order
   */
  static getExecutionOrder(template: WorkflowTemplate): string[] {
    const steps = template.workflow.steps;
    const visited = new Set<string>();
    const order: string[] = [];

    const visit = (stepId: string) => {
      if (visited.has(stepId)) return;
      
      const step = steps.find(s => s.id === stepId);
      if (!step) return;

      // Visit dependencies first
      step.dependencies.forEach(depId => visit(depId));
      
      visited.add(stepId);
      order.push(stepId);
    };

    // Start with steps that have no dependencies
    const entrySteps = steps.filter(s => s.dependencies.length === 0);
    entrySteps.forEach(step => visit(step.id));

    // Visit any remaining steps (in case of cycles or disconnected components)
    steps.forEach(step => visit(step.id));

    return order;
  }

  /**
   * Create a simple workflow from template (without approval gates)
   */
  static createSimpleWorkflow(template: WorkflowTemplate): Workflow {
    return {
      ...template.workflow,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Remove approval gates for simple execution
      steps: template.workflow.steps.filter(step => step.type !== StepType.APPROVAL_GATE)
    };
  }
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Template Registry with processing capabilities
 */
export class EnhancedTemplateRegistry {
  private templates = new Map<string, WorkflowTemplate>();

  registerTemplate(template: WorkflowTemplate): void {
    const validation = TemplateProcessor.validateTemplate(template);
    
    if (!validation.valid) {
      throw new Error(`Invalid template: ${validation.errors.join(', ')}`);
    }

    if (validation.warnings.length > 0) {
      console.warn(`Template warnings: ${validation.warnings.join(', ')}`);
    }

    this.templates.set(template.id, template);
  }

  getTemplate(id: string): WorkflowTemplate | null {
    return this.templates.get(id) || null;
  }

  processTemplate(id: string, userId?: string): ProcessedWorkflow | null {
    const template = this.getTemplate(id);
    if (!template) return null;

    return TemplateProcessor.processTemplate(template, userId);
  }

  getAllTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values());
  }

  getTemplatesByCategory(category: string): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .filter(t => t.workflow.metadata.category === category);
  }

  getFeaturedTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values()).filter(t => t.featured);
  }
}
